"""
Interrupt Handler

Implements a streamlined interrupt system with the exact 7-step flow:
1. TTS Playback
2. Interrupt Detection
3. TTS Pause
4. Acknowledgment
5. TTS Resume
6. Action Reversibility Check
7. Conditional Queuing

"""

import asyncio
import time
import numpy as np
from typing import Dict, Any, Optional

# Add webrtcvad import
try:
    import webrtcvad
    WEBRTCVAD_AVAILABLE = True
except ImportError:
    WEBRTCVAD_AVAILABLE = False

try:
    import sounddevice as sd
    SOUNDDEVICE_AVAILABLE = True
except ImportError:
    SOUNDDEVICE_AVAILABLE = False

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False

# PyAudio removed - using sounddevice for all audio operations

from core.logging.logger_config import get_module_logger
from schemas.outputSchema import StateOutput, StatusType, StatusCode

from core.config.interrupt_config import get_interrupt_config
from core.audio.noise_suppressor import get_noise_suppressor



class InterruptHandler:
    """
    Interrupt handler that implements the exact 7-step flow.
    Focuses on pause/resume functionality with conditional queuing.
    """
    
    def __init__(self, session_id: str, memory_manager, interrupt_config=None):
        self.session_id = session_id
        self.memory_manager = memory_manager
        self.interrupt_config = interrupt_config or get_interrupt_config()
        self.logger = get_module_logger("InterruptHandler", session_id=session_id)

        # 🎯 Initialize noise suppressor module
        self.noise_suppressor = get_noise_suppressor(session_id)
        
        # TTS playback state
        self.is_playing = False
        self.is_paused = False
        self.current_audio_path = None
        self.playback_start_time = None
        self.pause_time = None
        self.total_pause_duration = 0
        
        # Interrupt state
        self.interrupt_detected = False
        self.user_interrupt_input = None
        self.interrupt_position = 0.0  # Store position where interrupt occurred
        self.workflow_context = None  # Store workflow context for state determination

    def _interrupt_specific_vad(self, audio_samples: np.ndarray, sample_rate: int, threshold: float) -> dict:
        """
        🎯 ULTRA-CONSERVATIVE INTERRUPT-SPECIFIC VAD
        ============================================

        Specialized VAD for interrupt detection that minimizes false positives.
        Much more conservative than normal VAD to handle noisy environments.
        """
        try:
            if len(audio_samples) == 0:
                return {"has_voice": False, "energy": 0, "reason": "empty_audio"}

            # Calculate basic energy
            energy = np.mean(audio_samples.astype(np.float64) ** 2)

            # ULTRA-CONSERVATIVE CHECKS FOR INTERRUPTS

            # 1. Energy must be EXTREMELY high (much higher than fan noise)
            # Use 500x multiplier for ultra-conservative interrupt detection
            energy_check = energy > threshold
            significant_energy_check = energy > (threshold * 500)

            # 2. Must have clear speech characteristics
            zcr = np.mean(np.abs(np.diff(np.sign(audio_samples)))) / 2.0
            zcr_check = 0.02 <= zcr <= 0.3  # More lenient ZCR range to catch quiet speech

            # 3. Must be in optimal speech frequency range
            fft = np.fft.rfft(audio_samples)
            magnitude = np.abs(fft)
            freqs = np.fft.rfftfreq(len(audio_samples), 1/sample_rate)

            if np.sum(magnitude) > 0:
                spectral_centroid = np.sum(freqs * magnitude) / np.sum(magnitude)
            else:
                spectral_centroid = 0

            # Very strict spectral range (clear human speech only, avoid fan noise range)
            spectral_check = 600 <= spectral_centroid <= 2200

            # 4. Must have significant spectral complexity (not simple tones or fan noise)
            peak_threshold = np.max(magnitude) * 0.15  # Higher threshold
            significant_peaks = np.sum(magnitude > peak_threshold)
            complexity_check = significant_peaks > 8  # More peaks required

            # 5. Additional check: Reject if it looks like fan noise
            # Updated based on your actual fan characteristics: ZCR ~0.086, Centroid ~1815Hz
            fan_like = (1600 <= spectral_centroid <= 2000 and
                       0.07 <= zcr <= 0.12 and
                       10000 <= energy <= 200000)

            # ALL CHECKS MUST PASS and must NOT look like fan noise
            has_voice = (energy_check and significant_energy_check and zcr_check and
                        spectral_check and complexity_check and not fan_like)

            return {
                "has_voice": has_voice,
                "energy": float(energy),
                "zero_crossing_rate": float(zcr),
                "spectral_centroid": float(spectral_centroid),
                "spectral_peaks": int(significant_peaks),
                "threshold_used": float(threshold),
                "fan_like": fan_like,
                "checks": {
                    "energy_check": energy_check,
                    "significant_energy_check": significant_energy_check,
                    "zcr_check": zcr_check,
                    "spectral_check": spectral_check,
                    "complexity_check": complexity_check,
                    "not_fan_like": not fan_like
                },
                "reason": "interrupt_detected" if has_voice else "rejected_by_interrupt_vad"
            }

        except Exception as e:
            self.logger.warning(f"Interrupt-specific VAD failed: {e}")
            return {
                "has_voice": False,
                "energy": 0,
                "error": str(e),
                "reason": "error"
            }





        # Initialize pygame if available (fallback)
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init()
                self.logger.info("Pygame mixer initialized for TTS playback")
            except Exception as e:
                self.logger.warning(f"Failed to initialize pygame mixer: {e}")







    async def _check_for_interrupt(self) -> bool:
        """
        Step 2: Real-time interrupt detection with microphone monitoring

        This method should be called frequently during TTS playback to detect interrupts.
        """
        try:
            # First check if interrupts are enabled for the current state
            if not await self._is_interrupt_enabled_for_current_state(self.workflow_context):
                return False

            # First check memory for interrupt signal (for testing/external triggers)
            interrupt_context = await self.memory_manager.get_interrupt_context()
            if interrupt_context.get("detected") and not interrupt_context.get("handled"):
                self.user_interrupt_input = interrupt_context.get("user_input_queued", "User interrupted")

                # Mark as handled
                await self.memory_manager.set_interrupt_context(
                    detected=True,
                    handled=True,
                    user_input_queued=self.user_interrupt_input
                )

                return True

            # Real-time microphone monitoring for interrupts
            return await self._check_microphone_for_speech()

        except Exception as e:
            self.logger.error(f"Error checking for interrupt: {e}")
            return False

    async def _check_microphone_for_speech(self) -> bool:
        """
        Check microphone for speech activity using VAD (based on proven TTS interrupt monitor).
        Returns True if speech is detected (indicating an interrupt).
        """
        try:
            # Check if sounddevice is available
            if not SOUNDDEVICE_AVAILABLE:
                return False

            # Get microphone device from memory, use default if not set
            device_index = await self.memory_manager.get("microphone_device_index")

            # Use default microphone if none configured
            if device_index is None:
                self.logger.info("Using default microphone device for interrupt detection")

            # Use faster chunk duration for responsiveness
            sample_rate = 16000
            chunk_duration = 0.1  # 100ms chunks for faster checks
            channels = 1

            # Audio capture with error handling
            try:

                audio = sd.rec(int(chunk_duration * sample_rate),
                             samplerate=sample_rate,
                             channels=channels,
                             dtype='int16',
                             device=device_index)
                sd.wait()  # Wait for recording to complete
                audio_bytes = audio.tobytes()

                # 🎯 STEP 1: Apply noise suppression module FIRST
                audio_samples = np.frombuffer(audio_bytes, dtype=np.int16)
                filtered_samples, suppression_info = await self.noise_suppressor.process_audio_async(
                    audio_samples,
                    sample_rate
                )

                # 🎯 STEP 2: Use webrtcvad if available
                if WEBRTCVAD_AVAILABLE:
                    vad = webrtcvad.Vad()
                    aggressiveness = 3  # Most aggressive
                    vad.set_mode(aggressiveness)

                    # webrtcvad expects 16-bit mono PCM, 10/20/30ms frames
                    frame_duration = 10  # ms
                    frame_length = int(sample_rate * frame_duration / 1000)
                    bytes_per_sample = 2
                    num_frames = len(filtered_samples) // frame_length
                    speech_frames = 0
                    for i in range(num_frames):
                        frame = filtered_samples[i*frame_length:(i+1)*frame_length]
                        if len(frame) < frame_length:
                            continue
                        pcm_bytes = frame.astype(np.int16).tobytes()
                        if vad.is_speech(pcm_bytes, sample_rate):
                            speech_frames += 1
                    speech_ratio = speech_frames / max(1, num_frames)
                    self.logger.info(f"[webrtcvad] Energy: {energy:.1f}, Speech frames: {speech_frames}/{num_frames} (ratio: {speech_ratio:.2f})")
                    # Calculate energy to double-check
                    energy = np.mean(filtered_samples.astype(np.float64) ** 2)
                    energy_threshold = 2000  # Very high threshold

                    # MUCH MORE CONSERVATIVE: Require 80% speech frames AND high energy
                    if speech_ratio > 0.8 and energy > energy_threshold:
                        self.logger.info(f"� webrtcvad interrupt detected! Speech ratio: {speech_ratio:.2f}")
                        user_speech = await self._capture_and_transcribe_interrupt_audio()
                        self.user_interrupt_input = user_speech or "User interrupted during TTS playback"
                        return True
                    else:
                        self.logger.info(f"[webrtcvad] REJECTED - Speech ratio: {speech_ratio:.2f} (need >0.8), Energy: {energy:.1f} (need >{energy_threshold})")
                    return False
                else:
                    # Fallback to custom VAD
                    vad_threshold = self.interrupt_config.global_settings.vad_threshold if self.interrupt_config else 0.1
                    vad_result = self._interrupt_specific_vad(
                        filtered_samples,
                        sample_rate,
                        vad_threshold
                    )
                    if vad_result.get('has_voice', False):
                        self.logger.info(f"🎤 Custom VAD interrupt detected!")
                        user_speech = await self._capture_and_transcribe_interrupt_audio()
                        self.user_interrupt_input = user_speech or "User interrupted during TTS playback"
                        return True
                    return False

            except Exception as audio_error:
                self.logger.debug(f"Audio capture failed: {audio_error}")
                return False

        except Exception as e:
            self.logger.error(f"Error in microphone speech check: {e}")
            return False

    async def _simple_microphone_check(self) -> bool:
        """
        Simple microphone check using energy-based detection.
        Returns True if speech is detected (indicating an interrupt).
        """
        try:
            # First check if interrupts are enabled for the current state
            if not await self._is_interrupt_enabled_for_current_state(self.workflow_context):
                return False

            # Check if STT is currently recording (to avoid microphone conflicts)
            stt_recording = await self.memory_manager.get("stt_recording_active")
            if stt_recording:
                self.logger.debug("STT is recording, skipping interrupt detection to avoid microphone conflict")
                return False

            # Check if sounddevice is available
            if not SOUNDDEVICE_AVAILABLE:
                return False

            # Get microphone device from memory
            device_index = await self.memory_manager.get("microphone_device_index")

            if device_index is None:
                return False

            # Simple audio capture and energy detection
            sample_rate = 16000
            chunk_duration = 0.1  # 100ms for faster checks

            # Capture audio
            audio = sd.rec(int(chunk_duration * sample_rate),
                         samplerate=sample_rate,
                         channels=1,
                         dtype='int16',
                         device=device_index)
            sd.wait()

            # 🎯 Use noise suppressor module for simple check too
            audio_samples = np.frombuffer(audio.tobytes(), dtype=np.int16)

            try:
                from utils.audio_utils import enhanced_vad_check

                vad_threshold = self.interrupt_config.global_settings.vad_threshold if self.interrupt_config else 0.05

                # 🎯 Process through noise suppressor module first
                filtered_samples, suppression_info = await self.noise_suppressor.process_audio_async(
                    audio_samples,
                    sample_rate
                )

                # ULTRA-CONSERVATIVE interrupt VAD
                vad_result = self._interrupt_specific_vad(
                    filtered_samples,
                    sample_rate,
                    vad_threshold
                )

                if vad_result.get('has_voice', False):
                    energy = vad_result.get('energy', 0)
                    suppression_time = suppression_info.get('processing_time_ms', 0)

                    self.logger.info(
                        f"🎤 NOISE-SUPPRESSED simple interrupt detected! "
                        f"Energy: {energy:.0f}, Suppression: {suppression_time:.1f}ms, "
                        f"Filtered: {suppression_info.get('noise_suppression_applied', False)}"
                    )

                    # Capture and transcribe the actual user speech
                    user_speech = await self._capture_and_transcribe_interrupt_audio()
                    self.user_interrupt_input = user_speech or "User interrupted during TTS playback"

                    self.logger.info(f"📝 Captured user input: '{self.user_interrupt_input}'")
                    return True

                # Log energy levels for debugging (every 10th check)
                if hasattr(self, '_energy_check_count'):
                    self._energy_check_count += 1
                else:
                    self._energy_check_count = 1

                if self._energy_check_count % 10 == 0:
                    energy = vad_result.get('energy', 0)
                    self.logger.debug(f"🎤 Enhanced monitoring... Energy: {energy:.0f}")

                return False

            except ImportError:
                # Fallback to simple energy detection
                audio_squared = audio_samples.astype(np.float64) ** 2
                mean_squared = np.mean(audio_squared)
                audio_rms = np.sqrt(mean_squared) if mean_squared > 0 else 0.0

                energy_threshold = 800
                if audio_rms > energy_threshold:
                    self.logger.info(f"🎤 Fallback interrupt detected! RMS: {audio_rms:.1f}")
                    user_speech = await self._capture_and_transcribe_interrupt_audio()
                    self.user_interrupt_input = user_speech or "User interrupted during TTS playback"
                    return True

                return False

        except Exception as e:
            self.logger.debug(f"Simple microphone check failed: {e}")
            return False

    async def handle_tts_with_real_concurrent_monitoring(self, audio_path: str, workflow_context: Dict[str, Any]) -> StateOutput:
        """
        Enhanced version that uses TTSPlaybackController for real concurrent TTS playback and interrupt monitoring.

        This implements true concurrent monitoring:
        1. Starts real TTS audio playback
        2. Simultaneously monitors microphone for interrupts
        3. Pauses TTS immediately when interrupt detected
        4. Handles acknowledgment and resume
        """
        try:
            # Store workflow context for state determination
            self.workflow_context = workflow_context

            self.logger.info(
                "Starting TTS with real concurrent interrupt monitoring",
                action="handle_tts_with_real_concurrent_monitoring",
                input_data={"audio_path": audio_path, "state_id": workflow_context.get("state_id")}
            )

            # Step 1: Start real TTS playback with interrupt detection
            # Use the working audio playback method from filler TTS
            await self._play_tts_with_concurrent_monitoring(audio_path, workflow_context)

            # Check if interrupt occurred during playback
            interrupt_occurred = self.interrupt_detected

            if interrupt_occurred:
                self.logger.info("Real-time interrupt detected during TTS playback")

                # Simple acknowledgment -> resume flow
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS interrupted, acknowledged, and resumed",
                    code=StatusCode.OK,
                    outputs={
                        "interrupt_occurred": True,
                        "user_interrupt_input": self.user_interrupt_input
                    },
                    meta={"interrupt_flow": "completed"}
                )
            else:
                # TTS completed without interruption
                return StateOutput(
                    status=StatusType.SUCCESS,
                    message="TTS playback completed without interruption",
                    code=StatusCode.OK,
                    outputs={
                        "audio_path": audio_path,
                        "interrupt_occurred": False,
                        "action_reversible": "unknown",
                        "user_input_queued": False
                    },
                    meta={"playback": "completed"}
                )

        except Exception as e:
            self.logger.error(f"Error in real concurrent monitoring: {e}")
            return StateOutput(
                status=StatusType.ERROR,
                message=f"Concurrent monitoring error: {str(e)}",
                code=StatusCode.INTERNAL_ERROR,
                outputs={"interrupt_occurred": False},
                meta={"error": str(e)}
            )



    async def _play_tts_with_concurrent_monitoring(self, audio_path: str, workflow_context: Dict[str, Any]):
        """
        Play TTS audio with concurrent interrupt monitoring using the proven TTSPlaybackController.
        """
        try:
            self.logger.info(f"🎵 Starting TTS playback with real interrupt monitoring: {audio_path}")

            # Store TTS playback state
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="playing",
                playback_position=0.0
            )

            # Simple approach: Use pygame for audio + our own microphone monitoring
            self.logger.info("🎵 Starting simple TTS playback with direct microphone monitoring")

            # Initialize pygame for audio playback
            if PYGAME_AVAILABLE:
                pygame.mixer.quit()  # Quit any existing mixer
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                pygame.mixer.music.set_volume(1.0)

                # Load and start audio
                pygame.mixer.music.load(audio_path)
                pygame.mixer.music.play()

                self.logger.info(f"🎵 Pygame audio started - Volume: {pygame.mixer.music.get_volume()}")
                self.logger.info("� Starting direct microphone monitoring...")

                # Monitor for interrupts while audio is playing
                max_wait_time = 30
                wait_start = time.time()


                while pygame.mixer.music.get_busy():
                    # Check for interrupts every 100ms for faster response
                    interrupt_detected = await self._simple_microphone_check()

                    if interrupt_detected:
                        self.interrupt_detected = True

                        # Get exact playback position using pygame (your old working method!)
                        playback_position = pygame.mixer.music.get_pos() / 1000.0  # Convert ms to seconds
                        self.interrupt_position = playback_position  # Store for resume

                        # Stop pygame immediately to capture exact position
                        pygame.mixer.music.stop()
                        self.logger.info(f"🎯 Interrupt detected at {playback_position:.2f}s - pygame stopped!")

                        # Handle the interrupt (this plays acknowledgment)
                        await self._handle_interrupt_during_playback(workflow_context)

                        # Resume original TTS playback from exact position (your old working method!)
                        self.logger.info(f"▶️ Resuming original TTS from {playback_position:.2f} seconds...")
                        pygame.mixer.music.load(audio_path)
                        pygame.mixer.music.play(start=playback_position)  # ← This is your old working approach!

                        self.logger.info("✅ Original TTS playback resumed from exact position!")

                        # Reset interrupt flag for potential future interrupts
                        self.interrupt_detected = False

                    await asyncio.sleep(0.1)  # Check every 100ms

                    # Safety timeout
                    if time.time() - wait_start > max_wait_time:
                        self.logger.warning("⚠️ TTS playback timeout - forcing completion")
                        break

                if not self.interrupt_detected:
                    self.logger.info("✅ TTS playback completed without interruption")

            else:
                # Fallback if pygame not available
                self.logger.warning("Pygame not available - using fallback playback")
                await self._fallback_tts_playback(audio_path)

            # Update memory state to completed
            await self.memory_manager.set_tts_playback_state(
                audio_path=audio_path,
                status="completed",
                playback_position=0.0
            )

        except Exception as e:
            self.logger.error(f"Error in TTS playback with monitoring: {e}")
            # Fallback to simple playback
            await self._fallback_tts_playback(audio_path)

    async def _fallback_tts_playback(self, audio_path: str):
        """Fallback TTS playback using multiple methods."""
        try:
            # Try pygame first
            if PYGAME_AVAILABLE:
                try:
                    pygame.mixer.init()
                    pygame.mixer.music.load(audio_path)
                    pygame.mixer.music.play()

                    self.logger.info(f"🎵 Using pygame fallback for TTS: {audio_path}")

                    # Wait for playback to complete
                    while pygame.mixer.music.get_busy():
                        await asyncio.sleep(0.1)

                    self.logger.info("✅ Pygame fallback TTS playback completed")
                    return

                except Exception as pygame_error:
                    self.logger.warning(f"Pygame fallback failed: {pygame_error}")

            # Try playsound as last resort
            try:
                from playsound import playsound
                self.logger.info(f"🎵 Using playsound fallback for TTS: {audio_path}")

                # Run playsound in a thread to avoid blocking
                await asyncio.to_thread(playsound, audio_path)

                self.logger.info("✅ Playsound fallback TTS playback completed")

            except Exception as playsound_error:
                self.logger.error(f"Playsound fallback failed: {playsound_error}")
                self.logger.info(f"📁 TTS audio file available at: {audio_path}")
                self.logger.info("🔊 You can manually play the audio file to hear the TTS output")

        except Exception as e:
            self.logger.error(f"❌ All TTS playback methods failed: {e}")
            self.logger.info(f"📁 TTS audio file available at: {audio_path}")

    async def _handle_interrupt_during_playback(self, workflow_context: Dict[str, Any]):
        """Handle interrupt that occurred during TTS playback."""
        try:
            self.logger.info("🎤 Handling interrupt during TTS playback")

            # Step 4: Play acknowledgment message
            await self._play_acknowledgment_message()

            # Step 5: Resume TTS playback after acknowledgment
            await self._resume_tts_playback(self.interrupt_position)
            self.logger.info("✅ TTS playback resumed after interrupt acknowledgment")

        except Exception as e:
            self.logger.error(f"Error handling interrupt during playback: {e}")

    def _get_current_playback_position(self) -> float:
        """Get current playback position in seconds"""
        if not self.playback_start_time:
            return 0.0
        
        elapsed = time.time() - self.playback_start_time - self.total_pause_duration
        return max(0.0, elapsed)

    async def _pause_tts_playback(self):
        """Step 3: Pause TTS at current position"""
        if PYGAME_AVAILABLE and pygame.mixer.music.get_busy():
            pygame.mixer.music.pause()
            self.is_paused = True
            self.pause_time = time.time()
            
            self.logger.info("TTS playback paused due to interrupt")

    async def _play_acknowledgment_message(self):
        """Step 4: Play brief acknowledgment message from workflow config"""
        try:
            # Get acknowledgment message from workflow configuration
            acknowledgment_text = await self._get_workflow_interrupt_message(self.workflow_context)

            self.logger.info(f"🔊 Playing workflow acknowledgment: {acknowledgment_text}")

            # Generate TTS for acknowledgment
            try:
                from agents.tts.tts_agent import TTSAgent

                # Create TTS agent
                tts_agent = TTSAgent(session_id=self.session_id)

                # Generate acknowledgment audio
                tts_result = await tts_agent.text_to_speech(acknowledgment_text)

                if tts_result.status == StatusType.SUCCESS:
                    ack_audio_path = tts_result.outputs.get("audio_path")

                    if ack_audio_path and PYGAME_AVAILABLE:
                        # Play acknowledgment with pygame
                        pygame.mixer.music.load(ack_audio_path)
                        pygame.mixer.music.play()

                        self.logger.info("🔊 Playing acknowledgment audio...")

                        # Wait for acknowledgment to complete
                        while pygame.mixer.music.get_busy():
                            await asyncio.sleep(0.1)

                        self.logger.info("✅ Acknowledgment completed")
                    else:
                        self.logger.warning("No acknowledgment audio path or pygame unavailable")
                        await asyncio.sleep(1.0)  # Fallback pause
                else:
                    self.logger.error(f"Failed to generate acknowledgment TTS: {tts_result.message}")
                    await asyncio.sleep(1.0)  # Fallback pause

            except Exception as tts_error:
                self.logger.error(f"Error generating acknowledgment TTS: {tts_error}")
                # Fallback: just pause briefly
                await asyncio.sleep(1.0)

        except Exception as e:
            self.logger.error(f"Error playing acknowledgment: {e}")

    async def _is_interrupt_enabled_for_current_state(self, workflow_context: dict = None) -> bool:
        """Check if interrupts are enabled for the current state."""
        try:
            # First try to get current state from workflow context
            current_state = None
            if workflow_context and "state_id" in workflow_context:
                current_state = workflow_context["state_id"]

            if not current_state:
                # Try to get from memory
                current_state = await self.memory_manager.get("current_state")

            if not current_state:
                # Default to "Greeting" if we can't determine the state
                current_state = "Greeting"
                self.logger.warning(f"Could not determine current state for interrupt check, defaulting to: {current_state}")

            # Get workflow configuration
            workflow_name = await self.memory_manager.get("workflow_name")
            if not workflow_name:
                self.logger.warning("No workflow_name found in memory, assuming interrupts enabled")
                return True

            import json
            import os
            workflow_path = os.path.join("workflows", workflow_name)

            if os.path.exists(workflow_path):
                with open(workflow_path, 'r') as f:
                    workflow_config = json.load(f)

                # Navigate to the current state's interrupt config
                states = workflow_config.get("workflow", {}).get("states", {})
                state_config = states.get(current_state, {})
                interrupt_config = state_config.get("interrupt_config", {})
                enabled = interrupt_config.get("enabled", True)  # Default to True if not specified

                self.logger.info(f"🔍 Interrupt enabled for state '{current_state}': {enabled}")
                return enabled
            else:
                self.logger.error(f"Workflow file not found: {workflow_path}")
                return True  # Default to enabled if we can't read the config

        except Exception as e:
            self.logger.error(f"Error checking if interrupt is enabled for current state: {e}")
            return True  # Default to enabled on error

    async def _get_workflow_interrupt_message(self, workflow_context: dict = None) -> str:
        """Get the interrupt message from the current workflow state configuration."""
        try:
            # First try to get current state from workflow context (like backchanneling does)
            current_state = None
            if workflow_context and "state_id" in workflow_context:
                current_state = workflow_context["state_id"]
                self.logger.info(f"Got current state from workflow context: {current_state}")

            if not current_state:
                # Try multiple ways to get current state from memory
                current_state = await self.memory_manager.get("current_state")

            if not current_state:
                # Try alternative state keys
                current_state = await self.memory_manager.get("workflow_state")

            if not current_state:
                # Try to get from workflow context - check if we're in Inquiry state
                # Look for Inquiry-specific memory keys to determine current state
                inquiry_text = await self.memory_manager.get(f"{self.session_id}_Inquiry_clean_text")
                if inquiry_text:
                    current_state = "Inquiry"
                    self.logger.info(f"Detected current state from memory context: {current_state}")

            if not current_state:
                # Default to "Greeting" if we can't determine the state
                current_state = "Greeting"
                self.logger.warning(f"Could not determine current state, defaulting to: {current_state}")

            self.logger.info(f"🔍 Getting interrupt message for state: {current_state}")

            # Get workflow configuration
            workflow_name = await self.memory_manager.get("workflow_name")

            if workflow_name:
                import json
                import os

                workflow_path = os.path.join("workflows", workflow_name)
                self.logger.info(f"📁 Loading workflow config from: {workflow_path}")

                if os.path.exists(workflow_path):
                    with open(workflow_path, 'r') as f:
                        workflow_config = json.load(f)

                    # Navigate to the current state's interrupt config
                    states = workflow_config.get("workflow", {}).get("states", {})
                    state_config = states.get(current_state, {})
                    interrupt_config = state_config.get("interrupt_config", {})
                    interrupt_message = interrupt_config.get("interrupt_message")

                    if interrupt_message:
                        self.logger.info(f"📋 Using workflow interrupt message for state '{current_state}': {interrupt_message}")
                        return interrupt_message
                    else:
                        self.logger.warning(f"No interrupt_message found for state '{current_state}'")
                else:
                    self.logger.error(f"Workflow file not found: {workflow_path}")
            else:
                self.logger.warning("No workflow_name found in memory")

            # Fallback message
            fallback_message = "I hear you, let me finish first"
            self.logger.info(f"🔄 Using fallback interrupt message: {fallback_message}")
            return fallback_message

        except Exception as e:
            self.logger.error(f"Error getting workflow interrupt message: {e}")
            return "I hear you, let me finish first"  # Default fallback

    async def _capture_user_speech_during_interrupt(self, initial_audio: np.ndarray) -> Optional[str]:
        """
        Capture and transcribe user speech during interrupt.

        Args:
            initial_audio: Initial audio that triggered the interrupt

        Returns:
            str: Transcribed user speech, or None if transcription fails
        """
        try:
            self.logger.info("🎤 Capturing user speech for transcription...")

            # Record additional audio to get complete user utterance
            additional_duration = 2.0  # Record 2 more seconds
            sample_rate = 16000

            if SOUNDDEVICE_AVAILABLE:
                try:
                    # Use sounddevice for additional recording (consistent with rest of system)
                    additional_audio = sd.rec(
                        int(additional_duration * sample_rate),
                        samplerate=sample_rate,
                        channels=1,
                        dtype='int16'
                    )
                    sd.wait()  # Wait for recording to complete

                    # Convert to bytes for consistency
                    additional_frames = [additional_audio.tobytes()]

                    # Combine initial and additional audio
                    additional_audio = np.concatenate(additional_frames)
                    complete_audio = np.concatenate([initial_audio, additional_audio])

                except Exception as audio_error:
                    self.logger.warning(f"Failed to capture additional audio: {audio_error}")
                    complete_audio = initial_audio
            else:
                complete_audio = initial_audio

            # Transcribe the captured audio using STT
            transcribed_text = await self._transcribe_audio(complete_audio, sample_rate)

            if transcribed_text and transcribed_text.strip():
                self.logger.info(f"✅ Successfully transcribed: '{transcribed_text}'")
                return transcribed_text.strip()
            else:
                self.logger.warning("⚠️ Transcription returned empty result")
                return None

        except Exception as e:
            self.logger.error(f"Error capturing user speech: {e}")
            return None

    async def _transcribe_audio(self, audio_data: np.ndarray, sample_rate: int) -> Optional[str]:
        """
        Transcribe audio data to text using STT agent.

        Args:
            audio_data: Audio data as numpy array
            sample_rate: Sample rate of the audio

        Returns:
            str: Transcribed text, or None if transcription fails
        """
        try:
            # Save audio to temporary file
            import tempfile
            import wave

            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                temp_path = temp_file.name

                # Write audio data to WAV file
                with wave.open(temp_path, 'wb') as wav_file:
                    wav_file.setnchannels(1)  # Mono
                    wav_file.setsampwidth(2)  # 16-bit
                    wav_file.setframerate(sample_rate)
                    wav_file.writeframes(audio_data.tobytes())

            # Use STT agent to transcribe
            try:
                from agents.stt.stt_agent import STTAgent

                stt_agent = STTAgent(session_id=self.session_id)
                stt_result = await stt_agent.speech_to_text(temp_path)

                if stt_result.status == StatusType.SUCCESS:
                    transcribed_text = stt_result.outputs.get("text", "").strip()
                    self.logger.info(f"🎯 STT transcription: '{transcribed_text}'")
                    return transcribed_text
                else:
                    self.logger.error(f"STT failed: {stt_result.message}")
                    return None

            except Exception as stt_error:
                self.logger.error(f"STT agent error: {stt_error}")
                return None
            finally:
                # Clean up temporary file
                try:
                    import os
                    os.unlink(temp_path)
                except:
                    pass

        except Exception as e:
            self.logger.error(f"Error in audio transcription: {e}")
            return None

    async def _capture_and_transcribe_interrupt_audio(self, duration_sec: float = 3.0, sample_rate: int = 16000) -> str:
        """
        Capture real audio from microphone during interrupt and transcribe it to text.
        This is based on your old working approach from tts_interrupt_monitor.py!

        Args:
            duration_sec: Duration to record audio in seconds
            sample_rate: Audio sample rate

        Returns:
            str: Transcribed text from the captured audio
        """
        try:
            self.logger.info("🎤 Capturing real user audio during interrupt...")

            if SOUNDDEVICE_AVAILABLE:
                # Record audio using sounddevice (your old approach)
                audio_data = sd.rec(int(duration_sec * sample_rate),
                                  samplerate=sample_rate,
                                  channels=1,
                                  dtype='int16')
                sd.wait()  # Wait until recording is finished

                self.logger.info(f"✅ Audio captured ({duration_sec}s)")

                # Save to temporary WAV file for STT processing
                import tempfile
                import wave

                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as temp_file:
                    temp_audio_path = temp_file.name

                # Write WAV file
                with wave.open(temp_audio_path, 'wb') as wf:
                    wf.setnchannels(1)  # Mono
                    wf.setsampwidth(2)  # 16-bit
                    wf.setframerate(sample_rate)
                    wf.writeframes(audio_data.tobytes())

                self.logger.info("🔄 Audio saved, transcribing...")

                # Transcribe the captured audio using STT agent
                transcribed_text = await self._transcribe_audio_file(temp_audio_path)
                self.logger.info(f"✅ Transcription result: '{transcribed_text}'")

                # Clean up temporary file
                try:
                    import os
                    os.unlink(temp_audio_path)
                except:
                    pass  # Ignore cleanup errors

                return transcribed_text

            else:
                self.logger.warning("sounddevice not available - cannot capture real audio")
                return "[Audio capture not available]"

        except Exception as e:
            self.logger.error(f"Audio capture failed: {e}")
            return f"[Audio capture error: {str(e)}]"

    async def _transcribe_audio_file(self, audio_file_path: str) -> str:
        """
        Transcribe an audio file using the STT agent.
        Based on your old working approach!

        Args:
            audio_file_path: Path to the audio file to transcribe

        Returns:
            str: Transcribed text from the audio file
        """
        try:
            from agents.stt.stt_agent import STTAgent

            self.logger.info("🔄 Using STT agent to transcribe interrupt audio")

            # Create STT agent
            stt_agent = STTAgent(session_id=self.session_id)

            # Call STT agent
            stt_result = await stt_agent.speech_to_text(audio_file_path)

            if stt_result.status == StatusType.SUCCESS:
                transcribed_text = stt_result.outputs.get('text', '').strip()
                if transcribed_text:
                    self.logger.info(f"✅ STT transcription successful: '{transcribed_text}'")
                    return transcribed_text
                else:
                    self.logger.warning("⚠️ STT returned empty transcription")
                    return "[No speech detected in interrupt audio]"
            else:
                self.logger.error(f"❌ STT agent failed: {stt_result.message}")
                return f"[STT error: {stt_result.message}]"

        except Exception as e:
            self.logger.error(f"Audio transcription failed: {e}")
            return f"[Transcription error: {str(e)}]"



    async def _resume_tts_playback(self, resume_position: float):
        """Step 5: Resume TTS from exact pause position"""
        try:
            if self.is_paused and PYGAME_AVAILABLE:
                pygame.mixer.music.unpause()
                
                # Update pause duration tracking
                if self.pause_time:
                    self.total_pause_duration += time.time() - self.pause_time
                    self.pause_time = None
                
                self.is_paused = False
                
                self.logger.info(f"TTS playback resumed from position: {resume_position:.2f}s")
                
        except Exception as e:
            self.logger.error(f"Error resuming TTS playback: {e}")





    async def _execute_complete_workflow_for_queued_input(self, state_manager, enhanced_input: str):
        """
        Execute the complete StateManager workflow for queued input.
        This simulates a new user input going through the complete pipeline:
        STT (skipped, we have transcript) -> Preprocessing -> Processing -> TTS

        Args:
            state_manager: Reference to the StateManager
            enhanced_input: The enhanced user input with context
        """
        try:
            self.logger.info(
                "Starting complete workflow execution for queued input",
                action="_execute_complete_workflow_for_queued_input",
                input_data={"enhanced_input": enhanced_input},
                layer="interrupt_handler"
            )

            # Step 1: Preprocessing to extract intent
            await state_manager.transitionPipeline("preprocessing")
            self.logger.info("Step 1: Preprocessing - Extracting intent...")

            preprocessing_result = await state_manager.executePipelineState({"transcript": enhanced_input})
            self.logger.info(f"Preprocessing result status: {preprocessing_result.status.value}")

            if preprocessing_result.status.value != "success":
                self.logger.error(
                    f"Preprocessing failed for queued input: {preprocessing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )
                return

            # Extract preprocessing outputs
            intent = preprocessing_result.outputs.get("intent", "unknown")
            clean_text = preprocessing_result.outputs.get("clean_text", enhanced_input)
            emotion = preprocessing_result.outputs.get("emotion") or "neutral"
            gender = preprocessing_result.outputs.get("gender") or "female"

            self.logger.info(
                "Preprocessing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={
                    "intent": intent,
                    "clean_text": clean_text,
                    "emotion": emotion,
                    "gender": gender
                },
                layer="interrupt_handler"
            )

            # Step 2: Determine target state based on intent (if StateManager supports it)
            if hasattr(state_manager, '_get_target_state_for_intent'):
                target_state = state_manager._get_target_state_for_intent(intent)
                if target_state and target_state != state_manager.current_workflow_state_id:
                    self.logger.info(f"Step 2: Transitioning to target state: {target_state}")
                    await state_manager.transitionWorkflow(target_state)

            # Step 3: Execute processing
            await state_manager.transitionPipeline("processing")
            self.logger.info("Step 3: Processing - Executing processing state...")

            processing_result = await state_manager.executePipelineState({
                "clean_text": clean_text,
                "intent": intent,
                "emotion": emotion,
                "gender": gender
            })

            if processing_result.status.value != "success":
                self.logger.error(
                    f"Processing failed for queued input: {processing_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )
                return

            # Extract processing outputs
            ai_response = processing_result.outputs.get("llm_answer", "I'm sorry, I couldn't process your request.")

            self.logger.info(
                "Processing completed successfully",
                action="_execute_complete_workflow_for_queued_input",
                output_data={"ai_response": ai_response},
                layer="interrupt_handler"
            )

            # Step 4: Execute TTS
            await state_manager.transitionPipeline("tts")
            self.logger.info("Step 4: TTS - Generating response audio...")

            # Execute TTS with normal interrupt monitoring enabled for queued input
            # This ensures audio playback happens and new interrupts can be detected
            tts_result = await state_manager.executePipelineState({
                "text": ai_response,
                "emotion": emotion,
                "gender": gender
            })

            if tts_result.status.value == "success":
                self.logger.info(
                    "Complete workflow executed successfully for queued input",
                    action="_execute_complete_workflow_for_queued_input",
                    output_data={"tts_audio_path": tts_result.outputs.get("audio_path")},
                    layer="interrupt_handler"
                )

            else:
                self.logger.error(
                    f"TTS failed for queued input: {tts_result.message}",
                    action="_execute_complete_workflow_for_queued_input",
                    layer="interrupt_handler"
                )

        except Exception as e:
            self.logger.error(
                "Error executing complete workflow for queued input",
                action="_execute_complete_workflow_for_queued_input",
                reason=str(e),
                layer="interrupt_handler"
            )
            return False
