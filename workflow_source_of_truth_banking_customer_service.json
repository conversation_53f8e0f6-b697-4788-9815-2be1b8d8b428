{"id": "banking_customer_service", "name": "Banking Customer Service", "version": "2.1", "states": {"Greeting": {"type": "input", "layer2_id": "l2_greeting_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "Inquiry": {"type": "inform", "layer2_id": "l2_inquiry_banking_system_v2", "allowed_tools": ["STT", "LLM", "TTS", "CACHE"], "expected_input": ["text"], "expected_output": ["audio_path", "latencyTTS"]}, "RepeatedUserQuery": {"type": "inform", "layer2_id": "l2_repeated_user_query_simple", "allowed_tools": ["STT", "LLM", "TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}, "CheckBalance": {"type": "inform", "layer2_id": "l2_check_balance_banking_system_v2", "allowed_tools": ["LLM", "TTS", "DB_QUERY"], "expected_input": ["account_id"], "expected_output": ["audio_path", "latencyTTS"]}, "TransferFunds": {"type": "transaction", "layer2_id": "l2_transfer_funds_banking_system_v2", "allowed_tools": ["LLM", "TTS", "TRANSACTION_API"], "expected_input": ["source_account", "target_account", "amount"], "expected_output": ["audio_path", "latencyTTS"]}, "exchangeRate": {"type": "inform", "layer2_id": "l2_exchange_rate_banking_system_v2", "allowed_tools": ["LLM", "TTS", "EXCHANGE_API"], "expected_input": ["from_currency", "to_currency"], "expected_output": ["audio_path", "latencyTTS"]}, "Goodbye": {"type": "end", "layer2_id": "l2_goodbye_banking_system_v2", "allowed_tools": ["TTS"], "expected_input": [], "expected_output": ["audio_path", "latencyTTS"]}}, "rules": [{"from": "Greeting", "condition": "true", "to": "Inquiry"}, {"from": "Inquiry", "condition": "intent == 'account_balance'", "to": "CheckBalance"}, {"from": "Inquiry", "condition": "intent == 'fund_transfer'", "to": "TransferFunds"}, {"from": "Inquiry", "condition": "intent == 'exchange_rate'", "to": "exchangeRate"}, {"from": "Inquiry", "condition": "intent == 'goodbye'", "to": "Goodbye"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'account_balance'", "to": "CheckBalance"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'fund_transfer'", "to": "TransferFunds"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'exchange_rate'", "to": "exchangeRate"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "intent == 'goodbye'", "to": "Goodbye"}, {"from": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "CheckBalance", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "TransferFunds", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"from": "exchangeRate", "condition": "true", "to": "Repeated<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "allowed_actions": ["Check account balance", "Transfer funds", "Get exchange rates", "Multiple requests in same session"], "prohibited_actions": ["Do not share PINs or passwords", "Do not process transactions without verification"], "allowed_tools": ["STT", "TRANSACTION_API", "EXCHANGE_API", "TTS", "DB_QUERY", "LLM", "CACHE"]}